<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../models/Rpp.php';
require_once '../models/Guru.php';
require_once '../models/VisualQuestionTemplate.php';

// Get guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    die("Data guru tidak ditemukan");
}

// Get RPP list for this teacher
$rpp = new Rpp();
$rpp_list = $rpp->getByGuruId($guru_id);

// Get visual templates
$visualTemplate = new VisualQuestionTemplate();
$templates = $visualTemplate->getAll();
$subject_categories = $visualTemplate->getSubjectCategories();
$visual_types = $visualTemplate->getVisualTypes();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-magic"></i> Konfigurasi Generate Soal Visual AI
                    </h5>
                    <p class="text-muted mb-0">Buat soal dengan visualisasi interaktif menggunakan AI</p>
                </div>
                <div class="card-body">
                    <form id="visualGenerationForm" method="POST" action="process_visual_generation.php">
                        <!-- RPP Selection -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="rpp_id" class="form-label">Pilih RPP <span class="text-danger">*</span></label>
                                <select class="form-select" id="rpp_id" name="rpp_id" required>
                                    <option value="">-- Pilih RPP --</option>
                                    <?php while ($rpp_row = $rpp_list->fetch(PDO::FETCH_ASSOC)): ?>
                                        <option value="<?= $rpp_row['id'] ?>" 
                                                data-subject="<?= strtolower($rpp_row['nama_mapel']) ?>">
                                            <?= htmlspecialchars($rpp_row['tema_subtema']) ?> - 
                                            <?= htmlspecialchars($rpp_row['nama_mapel']) ?> 
                                            (<?= htmlspecialchars($rpp_row['nama_kelas']) ?>)
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="subject_category" class="form-label">Kategori Mata Pelajaran</label>
                                <select class="form-select" id="subject_category" name="subject_category">
                                    <option value="">-- Pilih Kategori --</option>
                                    <?php foreach ($subject_categories as $key => $value): ?>
                                        <option value="<?= $key ?>"><?= $value ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <!-- Question Configuration -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="fw-bold mb-3">Konfigurasi Soal</h6>
                            </div>
                            <div class="col-md-3">
                                <label for="multiple_choice_count" class="form-label">Pilihan Ganda</label>
                                <input type="number" class="form-control" id="multiple_choice_count" 
                                       name="multiple_choice_count" value="5" min="0" max="50">
                            </div>
                            <div class="col-md-3">
                                <label for="essay_count" class="form-label">Essay</label>
                                <input type="number" class="form-control" id="essay_count" 
                                       name="essay_count" value="3" min="0" max="20">
                            </div>
                            <div class="col-md-3">
                                <label for="multiple_choice_options" class="form-label">Opsi Pilihan Ganda</label>
                                <select class="form-select" id="multiple_choice_options" name="multiple_choice_options">
                                    <option value="4">4 Opsi (A-D)</option>
                                    <option value="5" selected>5 Opsi (A-E)</option>
                                </select>
                            </div>
                        </div>

                        <!-- Visual Configuration -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="fw-bold mb-3">Konfigurasi Visual</h6>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="enable_visuals" 
                                           name="enable_visuals" checked>
                                    <label class="form-check-label" for="enable_visuals">
                                        Aktifkan Elemen Visual
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label for="visual_question_count" class="form-label">Jumlah Soal dengan Visual</label>
                                <input type="number" class="form-control" id="visual_question_count" 
                                       name="visual_question_count" value="3" min="0" max="20">
                            </div>
                            <div class="col-md-4">
                                <label for="visual_complexity" class="form-label">Kompleksitas Visual</label>
                                <select class="form-select" id="visual_complexity" name="visual_complexity">
                                    <option value="simple">Sederhana</option>
                                    <option value="moderate" selected>Menengah</option>
                                    <option value="complex">Kompleks</option>
                                </select>
                            </div>
                        </div>

                        <!-- Visual Types Selection -->
                        <div class="row mb-4" id="visual_types_section">
                            <div class="col-12">
                                <h6 class="fw-bold mb-3">Jenis Visualisasi</h6>
                                <div class="row">
                                    <?php foreach ($visual_types as $key => $value): ?>
                                        <div class="col-md-3 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                       id="visual_type_<?= $key ?>" name="visual_types[]" value="<?= $key ?>">
                                                <label class="form-check-label" for="visual_type_<?= $key ?>">
                                                    <?= $value ?>
                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Difficulty Distribution -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="fw-bold mb-3">Distribusi Tingkat Kesulitan</h6>
                            </div>
                            <div class="col-md-3">
                                <label for="regular_count" class="form-label">Regular</label>
                                <input type="number" class="form-control" id="regular_count" 
                                       name="regular_count" value="3" min="0">
                            </div>
                            <div class="col-md-3">
                                <label for="hots_easy_count" class="form-label">HOTS Mudah</label>
                                <input type="number" class="form-control" id="hots_easy_count" 
                                       name="hots_easy_count" value="2" min="0">
                            </div>
                            <div class="col-md-3">
                                <label for="hots_medium_count" class="form-label">HOTS Sedang</label>
                                <input type="number" class="form-control" id="hots_medium_count" 
                                       name="hots_medium_count" value="2" min="0">
                            </div>
                            <div class="col-md-3">
                                <label for="hots_hard_count" class="form-label">HOTS Sulit</label>
                                <input type="number" class="form-control" id="hots_hard_count" 
                                       name="hots_hard_count" value="1" min="0">
                            </div>
                        </div>

                        <!-- Export Configuration -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="fw-bold mb-3">Konfigurasi Export</h6>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="include_visuals_in_export" 
                                           name="include_visuals_in_export" checked>
                                    <label class="form-check-label" for="include_visuals_in_export">
                                        Sertakan Visual dalam Export
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="export_image_quality" class="form-label">Kualitas Gambar Export</label>
                                <select class="form-select" id="export_image_quality" name="export_image_quality">
                                    <option value="low">Rendah (Cepat)</option>
                                    <option value="medium" selected>Sedang</option>
                                    <option value="high">Tinggi (Lambat)</option>
                                </select>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-magic"></i> Generate Soal Visual
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                        <i class="fas fa-undo"></i> Reset
                                    </button>
                                    <a href="generate_questions.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left"></i> Kembali ke Generate Biasa
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Auto-detect subject category based on RPP selection
    $('#rpp_id').change(function() {
        const selectedOption = $(this).find('option:selected');
        const subject = selectedOption.data('subject');
        
        // Map common subject names to categories
        const subjectMapping = {
            'matematika': 'mathematics',
            'fisika': 'physics',
            'biologi': 'biology',
            'kimia': 'chemistry',
            'ekonomi': 'economics',
            'akuntansi': 'accounting',
            'sejarah': 'history',
            'geografi': 'geography',
            'bahasa': 'language',
            'pkn': 'civics',
            'seni': 'arts'
        };
        
        if (subject && subjectMapping[subject]) {
            $('#subject_category').val(subjectMapping[subject]);
            updateVisualTypes(subjectMapping[subject]);
        }
    });

    // Update visual types based on subject category
    $('#subject_category').change(function() {
        updateVisualTypes($(this).val());
    });

    // Toggle visual configuration
    $('#enable_visuals').change(function() {
        if ($(this).is(':checked')) {
            $('#visual_types_section').show();
            $('#visual_question_count').prop('disabled', false);
        } else {
            $('#visual_types_section').hide();
            $('#visual_question_count').prop('disabled', true);
        }
    });

    // Auto-calculate total questions
    $('input[type="number"]').on('input', function() {
        updateTotalQuestions();
    });

    function updateVisualTypes(category) {
        // Reset all checkboxes
        $('input[name="visual_types[]"]').prop('checked', false);
        
        // Auto-select appropriate visual types based on subject
        const recommendations = {
            'mathematics': ['graph', 'chart', 'interactive'],
            'physics': ['graph', 'animation', 'simulation'],
            'biology': ['chart', 'animation', 'interactive'],
            'chemistry': ['3d_model', 'animation', 'graph'],
            'economics': ['graph', 'chart', 'table'],
            'accounting': ['table', 'chart'],
            'history': ['map', 'interactive'],
            'geography': ['map', 'chart'],
            'language': ['interactive'],
            'civics': ['interactive'],
            'arts': ['interactive', 'animation']
        };
        
        if (recommendations[category]) {
            recommendations[category].forEach(type => {
                $('#visual_type_' + type).prop('checked', true);
            });
        }
    }

    function updateTotalQuestions() {
        const mcCount = parseInt($('#multiple_choice_count').val()) || 0;
        const essayCount = parseInt($('#essay_count').val()) || 0;
        const total = mcCount + essayCount;
        
        // Update visual question count max
        $('#visual_question_count').attr('max', total);
        
        // Ensure visual count doesn't exceed total
        const visualCount = parseInt($('#visual_question_count').val()) || 0;
        if (visualCount > total) {
            $('#visual_question_count').val(total);
        }
    }
});

function resetForm() {
    document.getElementById('visualGenerationForm').reset();
    $('#subject_category').val('');
    $('input[name="visual_types[]"]').prop('checked', false);
}
</script>

<?php require_once '../template/footer.php'; ?>
